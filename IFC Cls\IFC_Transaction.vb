Public Class IFC_Transaction
    Dim Conn As New Conn_Cls
    'Dim ClsOnHand As New Cls_StockOnHand
    'Dim Clsprod As New Cls_Products
    'Dim ClsPatch As New Cls_Patch
    Dim ClsOnHand As New OnHand_Cls
    Public Function LoadPosDataOnLine(Fromdate As DateTime, Todate As DateTime, Optional IsLoding As Boolean = False) As DataTable
        Dim SqlStr As String = ""
        Dim CompanyPOS_Id, OutLetPOS_Id, CostCenterPOS_Id, FoDates, ToDates, SqlPul As String
        CompanyPOS_Id = ""
        OutLetPOS_Id = ""
        CostCenterPOS_Id = ""
        SqlPul = ""
        If IsLoding Then SqlPul = " and plu=0 "
        Dim DtScm As New DataTable
        DtScm = Conn.SELECT_TXT(" select * from POSSetting ")
        'If DtScm.Rows.Count > 0 Then
        For R As Integer = 0 To DtScm.Rows.Count - 1


            If CompanyPOS_Id = "" Then
                CompanyPOS_Id = DtScm.Rows(R)("Company_IdPOS")
            Else
                CompanyPOS_Id = CompanyPOS_Id & "," & DtScm.Rows(R)("Company_IdPOS")
            End If


            If OutLetPOS_Id = "" Then
                OutLetPOS_Id = DtScm.Rows(R)("Brand_IdPOS")
            Else
                OutLetPOS_Id = OutLetPOS_Id & "," & DtScm.Rows(R)("Brand_IdPOS")
            End If


            If CostCenterPOS_Id = "" Then
                CostCenterPOS_Id = DtScm.Rows(R)("CostCenter_IdPOS")
            Else
                CostCenterPOS_Id = CostCenterPOS_Id & "," & DtScm.Rows(R)("CostCenter_IdPOS")
            End If
        Next

        FoDates = Format(Fromdate, "yyyy/MM/dd 00:00:00")
        ToDates = Format(Todate, "yyyy/MM/dd 23:59:59")
        If Conn.POS = "Matrix POS" Then
            'SqlStr = "  SELECT   mandant AS CompanyPOS_Id, outlet AS OutLetPOS_Id, center AS CostCenterPOS_Id, centername AS CostCenterPOS_Name, plu AS Product_Code, article AS Product_Name, SUM(Qty) AS Reciving_Q, "
            'SqlStr = SqlStr & "      price AS Sales_Price, SUM(Amount) AS CostTotalLine, SUM(discount) AS discount, SUM(0 * (Amount_10 + Amount_12)) AS taxamount, statistdate AS Transaction_Date_Create, payform AS MethodOfPayment_Id, "
            'SqlStr = SqlStr & "          payformname AS MethodOfPayment_Name, billnum AS Check_No"
            'SqlStr = SqlStr & "      FROM dbo.v_Orders"
            'SqlStr = SqlStr & "     GROUP BY outlet, statistdate, mandant, plu, center, centername, price, article, payform, payformname, billnum"
            'SqlStr = SqlStr & "     HAVING (mandant =" & CompanyPOS_Id & ") AND (outlet =" & OutLetPOS_Id & ") AND (center =" & CostCenterPOS_Id & ")"
            'SqlStr = SqlStr & "     ORDER BY CompanyPOS_Id, OutLetPOS_Id, Product_Code"

            SqlStr = " select * from v_ProcIFC "
            SqlStr = SqlStr & " where mandant in (" & CompanyPOS_Id & ") and outlet in (" & OutLetPOS_Id & ") and center in (" & CostCenterPOS_Id & ") "
            ' SqlStr = SqlStr & " and (statistdate >= CONVERT(DATETIME, '" & FoDates & "', 102)) AND (statistdate <= CONVERT(DATETIME,'" & ToDates & "', 102))"
            SqlStr = SqlStr & " And IsIFCDone=0  "
            SqlStr = SqlStr & SqlPul & "  ORDER BY mandant, outlet, center,plu "
        End If


        If Conn.POS = "Smart POS" Then
            CheckExistV_ProcIFC()
            SqlStr = " select * from v_ProcIFC "
            SqlStr = SqlStr & " where mandant in (" & CompanyPOS_Id & ") and outlet in (" & OutLetPOS_Id & ") and center in (" & CostCenterPOS_Id & ") "
            '     SqlStr = SqlStr & " and (statistdate >= CONVERT(DATETIME, '" & FoDates & "', 102)) AND (statistdate <= CONVERT(DATETIME,'" & ToDates & "', 102))"
            SqlStr = SqlStr & " And IsIFCDone=0  "
            SqlStr = SqlStr & SqlPul & "  ORDER BY mandant, outlet, center,plu "
        End If

        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXTPOS(SqlStr)

        Return Dt

    End Function

    Public Sub UpdateCHDIDOnline(ChDID As Integer)
        Conn.EXECUT_TxtPOS("Update Check_Details Set IsIFCDone=1 Where ChDID=" & ChDID & "")
    End Sub
    Public Function LoadPosData(Fromdate As DateTime, Todate As DateTime, CostCenter_IdPOS As Integer, Optional IsLoding As Boolean = False) As DataTable
        Dim SqlStr As String = ""
        Dim CompanyPOS_Id, OutLetPOS_Id, CostCenterPOS_Id, FoDates, ToDates, SqlPul As String
        CompanyPOS_Id = ""
        OutLetPOS_Id = ""
        CostCenterPOS_Id = ""
        SqlPul = ""
        If IsLoding Then SqlPul = " and plu=0 "
        Dim DtScm As New DataTable
        DtScm = Conn.SELECT_TXT(" select * from POSSetting Where CostCenter_IdPOS=" & CostCenter_IdPOS & "")
        'If DtScm.Rows.Count > 0 Then
        For R As Integer = 0 To DtScm.Rows.Count - 1


            If CompanyPOS_Id = "" Then
                CompanyPOS_Id = DtScm.Rows(R)("Company_IdPOS")
            Else
                CompanyPOS_Id = CompanyPOS_Id & "," & DtScm.Rows(R)("Company_IdPOS")
            End If


            If OutLetPOS_Id = "" Then
                OutLetPOS_Id = DtScm.Rows(R)("Brand_IdPOS")
            Else
                OutLetPOS_Id = OutLetPOS_Id & ", " & DtScm.Rows(R)("Brand_IdPOS")
            End If


            If CostCenterPOS_Id = "" Then
                CostCenterPOS_Id = DtScm.Rows(R)("CostCenter_IdPOS")
            Else
                CostCenterPOS_Id = CostCenterPOS_Id & "," & DtScm.Rows(R)("CostCenter_IdPOS")
            End If
        Next

        FoDates = Format(Fromdate, "yyyy/MM/dd 00:00:00")
        ToDates = Format(Todate, "yyyy/MM/dd 23:59:59")
        If Conn.POS = "Matrix POS" Then
            'SqlStr = "  SELECT   mandant AS CompanyPOS_Id, outlet AS OutLetPOS_Id, center AS CostCenterPOS_Id, centername AS CostCenterPOS_Name, plu AS Product_Code, article AS Product_Name, SUM(Qty) AS Reciving_Q, "
            'SqlStr = SqlStr & "      price AS Sales_Price, SUM(Amount) AS CostTotalLine, SUM(discount) AS discount, SUM(0 * (Amount_10 + Amount_12)) AS taxamount, statistdate AS Transaction_Date_Create, payform AS MethodOfPayment_Id, "
            'SqlStr = SqlStr & "          payformname AS MethodOfPayment_Name, billnum AS Check_No"
            'SqlStr = SqlStr & "      FROM dbo.v_Orders"
            'SqlStr = SqlStr & "     GROUP BY outlet, statistdate, mandant, plu, center, centername, price, article, payform, payformname, billnum"
            'SqlStr = SqlStr & "     HAVING (mandant =" & CompanyPOS_Id & ") AND (outlet =" & OutLetPOS_Id & ") AND (center =" & CostCenterPOS_Id & ")"
            'SqlStr = SqlStr & "     ORDER BY CompanyPOS_Id, OutLetPOS_Id, Product_Code"

            SqlStr = " select * from v_ProcIFC "
            SqlStr = SqlStr & " where mandant in (" & CompanyPOS_Id & ") and outlet in (" & OutLetPOS_Id & ") and center in (" & CostCenterPOS_Id & ") "
            SqlStr = SqlStr & " and (statistdate >= CONVERT(DATETIME, '" & FoDates & "', 102)) AND (statistdate <= CONVERT(DATETIME,'" & ToDates & "', 102))"
            SqlStr = SqlStr & SqlPul & "  ORDER BY mandant, outlet, center,plu "
        End If


        If Conn.POS = "Smart POS" Then
            CheckExistV_ProcIFC()
            SqlStr = " select * from v_ProcIFC "
            SqlStr = SqlStr & " where mandant in (" & CompanyPOS_Id & ") and outlet in (" & OutLetPOS_Id & ") and center in (" & CostCenterPOS_Id & ") "
            SqlStr = SqlStr & " and (statistdate >= CONVERT(DATETIME, '" & FoDates & "', 102)) AND (statistdate <= CONVERT(DATETIME,'" & ToDates & "', 102))"
            SqlStr = SqlStr & SqlPul & "  ORDER BY mandant, outlet, center,plu "
        End If

        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXTPOS(SqlStr)

        Return Dt

    End Function
    Public Sub CheckExistV_ProcIFC()
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXTPOS("SELECT * FROM sys.objects WHERE object_id=OBJECT_ID('v_ProcIFC') ")
        If Dt.Rows.Count > 0 Then Return
        Dim Sql As String = ""

        If Conn.POS = "Smart POS" Then
            '            Sql = "CREATE VIEW  v_ProcIFC
            'AS
            'SELECT CCGCode AS mandant, CCCode AS outlet, FloorID AS center, FloorName AS centername, ArtCode AS plu, ArtName AS article, SUM(NetQty) AS Qty, AVG(UnitPrice) AS price, SUM(NetPrice) AS Amount, SUM(DeducAmount) AS discount,
            '                  SUM(ChkTaxesAmount_1 + ChkTaxesAmount_2) AS taxamount, SysDate AS statistdate, OTCode AS payform, OTName AS payformname, SUM(ChkPrtID) AS billnum
            'FROM     dbo.vOrders
            'GROUP BY CCGCode, CCCode, FloorID, FloorName, ArtCode, ArtName, SysDate, OTCode, OTName"


            Sql = "CREATE VIEW [dbo].[v_ProcIFC]
AS
SELECT        CCGCode AS mandant, CCCode AS outlet, FloorID AS center, FloorName AS centername, ArtCode AS plu, ArtName AS article, SUM(NetQty) AS Qty, AVG(UnitPrice) AS price, SUM(NetPrice) AS Amount,
                         SUM(DeducAmount) AS discount, SUM(ChkTaxesAmount_1 + ChkTaxesAmount_2) AS taxamount, SysDate AS statistdate, SUM(CASE (dbo.vOrders.IsIFCDone) WHEN 1 THEN 1 ELSE 0 END) AS IsIFCDone,
                         0 AS ChDID
FROM            dbo.vOrders
GROUP BY CCGCode, CCCode, FloorID, FloorName, ArtCode, ArtName, SysDate
"

            Conn.EXECUT_TxtPOS(Sql)
        End If

    End Sub
    Public Function GetLasttrans_date(Dte As DateTime) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        Dim DteFirts, DteLast As String
        DteFirts = Dte.ToString("yyyy/MM/dd 00:00:00")
        DteLast = Dte.ToString("yyyy/MM/dd 23:59:59")
        Dt = Conn.SELECT_TXT("select * from Sales_POS where (Transaction_Date_Create >= CONVERT(DATETIME, '" & DteFirts & "', 102)) AND (Transaction_Date_Create <= CONVERT(DATETIME,'" & DteLast & "', 102)) order by Transaction_Date_Create desc")
        Return Dt
    End Function

    Public Function SalePosLoadInDGVSCM() As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("select * FROM     Sales_POS where Ser=0")
        Return Dt
    End Function

    Public Function GetSettingPOS(CompanyId As Integer, outLetId As Integer, CostID As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("select * FROM      POSSetting where Company_IdPOS='" & CompanyId & "' and Brand_IdPOS='" & outLetId & "' and CostCenter_IdPOS='" & CostID & "'")
        Return Dt
    End Function

    Public Function GetProductData(ProductCode As String) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        If ProductCode = "11203" Then
            Dim xxx As String = ""
        End If
        Dt = Conn.SELECT_TXT("select * FROM      ProductsTbl where Product_Code='" & ProductCode & "' and (IsSales = 1)")
        Return Dt
    End Function
    ', CostCenter_Id As Integer
    Public Function GetSOHData(Product_Id As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("select * FROM      StockOnHandTbl where Product_Id=" & Product_Id & " order by CostCenter_Id ")
        Return Dt
    End Function
    Public Function GetSOHDataGetting(Product_Id As Integer, Sql As String) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        'order by CostCenter_Id
        Dt = Conn.SELECT_TXT("select * FROM      StockOnHandTbl where Product_Id=" & Product_Id & " " + Sql)
        Return Dt
    End Function
    Public Function GetExpiredData(Product_Id As Integer, CostCenter_Id As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("select * FROM  Patches where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & "  and CloseOpen=0 and usd=0 order by Exp_Date ")
        Return Dt
    End Function

    Public Function GetCostCenterData(CostCenter_Id As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        '   Dt = Conn.SELECT_TXT("select * FROM       CostCenterTbl where Store_id=" & CostCenter_Id & "  ")
        Dt = Conn.SELECT_TXT("select * from CostCenterLinkPOS where Ser=" & CostCenter_Id & " ")
        Return Dt
    End Function
    Public Function GetCostCenterDataTrue(CostCenter_Id As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("select * FROM       CostCenterTbl where CostCenter_Id=" & CostCenter_Id & "  ")
        Return Dt
    End Function
    Public Sub UpdateUsePatch(Patch_Ser As Integer, Product_Id As Integer, CostCenter_Id As Integer)
        Conn.EXECUT_Txt("Update Patches set usd=1 where Patch_Ser=" & Patch_Ser & " and Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & "")
    End Sub

    Public Function GetSOHData(Product_Id As Integer, CostCenter_Id As Integer) As DataTable
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("select * FROM      StockOnHandTbl where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & " ")
        Return Dt
    End Function

    Public Function GetTransCodeSales() As Integer
        Dim SqlStr As String
        Dim Id As Integer = 0
        Dim Dt As New DataTable
        SqlStr = "select ISNULL(MAX(Transaction_Code)+1,1) from Transaction_HeadTbl where Transaction_Id=9 "
        Dt = Conn.SELECT_TXT(SqlStr)
        Id = Dt.Rows(0)(0)
        Return Id
    End Function

    Public Function GetTransCodeProduction() As Integer
        Dim SqlStr As String
        Dim Id As Integer = 0
        Dim Dt As New DataTable
        SqlStr = "select ISNULL(MAX(Transaction_Code)+1,1) from Transaction_HeadTbl where Transaction_Id=7 "
        Dt = Conn.SELECT_TXT(SqlStr)
        Id = Dt.Rows(0)(0)
        Return Id
    End Function

    Public Sub Head_Insert(Transaction_Code As String, CostCenter_Id As Integer, Suppliers_Id As Integer, CostCenter_Supplier As String, Remarks As String, Invoice_No As String, Amount_Bill As Double, Tax_Bill As Double, Total_Amount As Double, Transaction_Save As Boolean, Transaction_Submit As Boolean, User_Id As Integer, Utholization_Id As Integer, Transaction_PaidAmount As Double, Transaction_NetAmount As Double, Authulized As Boolean, Transaction_Date As DateTime, Transaction_Date_Submit As DateTime, Transaction_Patch As String, User_Id_Submit As Integer, Invoice_NoReciving As String, TransId As Integer)


        Dim SqlStr, SubmitDate, transDate As String
        'SubmitDate = Format(Transaction_Date_Submit, "yyyy-MM-dd")
        'transDate = Format(Transaction_Date, "yyyy-MM-dd")
        transDate = Transaction_Date.ToString("yyyy-MM-dd HH:mm:ss")
        SubmitDate = Transaction_Date_Submit.ToString("yyyy-MM-dd HH:mm:ss")

        If Transaction_Patch = "" Then

            ',User_IdAccept
            SqlStr = " INSERT INTO Transaction_HeadTbl (Transaction_Code ,CostCenter_Id,Suppliers_Id,CostCenter_Supplier,Remarks,Invoice_No,Amount_Bill,Tax_Bill,Total_Amount,Transaction_Save,Transaction_Submit,User_Id,Utholization_Id,Transaction_PaidAmount,Transaction_NetAmount,Authulized,Transaction_Id,Transaction_Date,Transaction_Date_Submit,Accept_Reciving,User_Id_Submit,Invoice_NoReciving) VALUES"
            SqlStr = SqlStr & "('" & Transaction_Code & "' ," & CostCenter_Id & "," & Suppliers_Id & ",'" & CostCenter_Supplier & "','" & Remarks & "','" & Invoice_No & "','" & Amount_Bill & "','" & Tax_Bill & "','" & Total_Amount & "','" & Transaction_Save & "','" & Transaction_Submit & "'," & User_Id & "," & Utholization_Id & ",'" & Transaction_PaidAmount & "','" & Transaction_NetAmount & "','" & Authulized & "'," & TransId & ",'" & transDate & "','" & SubmitDate & "',0," & User_Id_Submit & ",'" & Invoice_NoReciving & "')"
        Else
            SqlStr = " INSERT INTO Transaction_HeadTbl (Transaction_Code ,CostCenter_Id,Suppliers_Id,CostCenter_Supplier,Remarks,Invoice_No,Amount_Bill,Tax_Bill,Total_Amount,Transaction_Save,Transaction_Submit,User_Id,Utholization_Id,Transaction_PaidAmount,Transaction_NetAmount,Authulized,Transaction_Id,Transaction_Date,Transaction_Date_Submit,Transaction_Patch,Accept_Reciving,User_Id_Submit,Invoice_NoReciving) VALUES"
            SqlStr = SqlStr & " ('" & Transaction_Code & "' ," & CostCenter_Id & "," & Suppliers_Id & ",'" & CostCenter_Supplier & "','" & Remarks & "','" & Invoice_No & "','" & Amount_Bill & "','" & Tax_Bill & "','" & Total_Amount & "','" & Transaction_Save & "','" & Transaction_Submit & "'," & User_Id & "," & Utholization_Id & ",'" & Transaction_PaidAmount & "','" & Transaction_NetAmount & "','" & Authulized & "'," & TransId & ",'" & transDate & "','" & SubmitDate & "','" & Transaction_Patch & "',0," & User_Id_Submit & ",'" & Invoice_NoReciving & "')"

        End If
        Conn.EXECUT_Txt(SqlStr)
        ','" & Transaction_Patch & "'
        Dim Dt As New DataTable
        Dt = ShowHeadWithConditions_(" and Transaction_Code='" & Transaction_Code & "'", TransId)
        If Dt.Rows.Count > 0 Then
            Transaction_Patch = Dt.Rows(0)("Transaction_Patch").ToString()
        End If

        Dim SqlStrHestory As String
        SqlStrHestory = " INSERT INTO Transaction_Head_HistoryTbl (Transaction_Code ,CostCenter_Id,Suppliers_Id,CostCenter_Supplier,Remarks,Invoice_No,Amount_Bill,Tax_Bill,Total_Amount,Transaction_Save,Transaction_Submit,User_Id,Utholization_Id,Transaction_PaidAmount,Transaction_NetAmount,Authulized,Transaction_Id,Transaction_Date,Transaction_Date_Submit,Transaction_Patch,Accept_Reciving,User_Id_Submit,Invoice_NoReciving) VALUES"
        SqlStrHestory = SqlStrHestory & " ('" & Transaction_Code & "' ," & CostCenter_Id & "," & Suppliers_Id & ",'" & CostCenter_Supplier & "','" & Remarks & "','" & Invoice_No & "','" & Amount_Bill & "','" & Tax_Bill & "','" & Total_Amount & "','" & Transaction_Save & "','" & Transaction_Submit & "'," & User_Id & "," & Utholization_Id & ",'" & Transaction_PaidAmount & "','" & Transaction_NetAmount & "','" & Authulized & "'," & TransId & ",'" & transDate & "','" & SubmitDate & "','" & Transaction_Patch & "',0," & User_Id_Submit & ",'" & Invoice_NoReciving & "')"

        Conn.EXECUT_Txt(SqlStrHestory)
    End Sub
    Public Function ShowHeadWithConditions_(Conditions_ As String, TransId As Integer)
        Dim DT As New DataTable
        DT.Clear()
        Dim SqlStr As String
        SqlStr = "SELECT   Transaction_Code, CostCenter_Id, Suppliers_Id, CostCenter_Supplier, Remarks, Invoice_No, Amount_Bill, Tax_Bill, Total_Amount, Transaction_Save, Transaction_Submit, User_Id, Utholization_Id, "
        SqlStr = SqlStr & " Transaction_PaidAmount, Transaction_NetAmount, Authulized, Ser, Transaction_Id, Transaction_Date, Transaction_Date_Submit, Transaction_Patch, Accept_Reciving, User_Id_Submit, User_IdAccept,Invoice_NoReciving "
        SqlStr = SqlStr & " FROM Transaction_HeadTbl where Ser is not null  AND (Transaction_Id =" & TransId & ") " & Conditions_ & ""

        DT = Conn.SELECT_TXT(SqlStr)
        Return DT

    End Function

    Public Function GetPatchHead(Transaction_Code As Integer, Transaction_Id As Integer) As String
        Dim SqlStr As String
        Dim Patches As String = ""
        Dim Dt As New DataTable
        SqlStr = "select * from Transaction_HeadTbl where Transaction_Code=" & Transaction_Code & " and Transaction_Id=" & Transaction_Id & " "
        Dt = Conn.SELECT_TXT(SqlStr)
        If Dt.Rows.Count > 0 Then
            Patches = Dt.Rows(0)("Transaction_Patch").ToString()
        End If

        Return Patches
    End Function

    Public Sub UpdateHeadAmount(Transaction_Code As Integer, Transaction_Id As Integer, Amount As Decimal)

        Conn.EXECUT_Txt("Update Transaction_HeadTbl set Amount_Bill='" & Amount & "',Total_Amount='" & Amount & "',Transaction_NetAmount='" & Amount & "'   where Transaction_Code=" & Transaction_Code & " and Transaction_Id=" & Transaction_Id & "")
    End Sub

    Public Sub UpdateSOH(Product_Id As Integer, CostCenter_Id As Integer, Quantity As Decimal, BaseUnit As Decimal)


        Conn.EXECUT_Txt("Update  StockOnHandTbl set  Quntity='" & Quantity & "',QuntityBase='" & BaseUnit & "' where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & " ")
    End Sub

    Public Sub UpdatePatchExpired(PatchSer As Integer, Quanitity As Decimal)
        Dim PatchQuantity, Deff As Decimal
        Dim CloseOpen As Boolean
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("select * from Patches where Patch_Ser=" & PatchSer & "")

        Quanitity = Math.Abs(Quanitity)
        If Dt.Rows.Count > 0 Then
            PatchQuantity = Convert.ToDecimal(Dt.Rows(0)("NetQ_Qsetup_CurrentQ"))
            Deff = Quanitity - PatchQuantity
            If Quanitity >= PatchQuantity Then
                CloseOpen = True
                Conn.EXECUT_Txt("Update Patches set NetQ_Qsetup_CurrentQ='" & Deff & "',usd=0,CloseOpen='" & CloseOpen & "'  where Patch_Ser=" & PatchSer & "")
            Else

                Conn.EXECUT_Txt("Update Patches set NetQ_Qsetup_CurrentQ='" & Deff & "',usd=0,CloseOpen='" & CloseOpen & "'  where Patch_Ser=" & PatchSer & "")
            End If


        End If
    End Sub

    Public Sub UpdatePatcheUseFalse(PatchSer As String)

        Conn.EXECUT_Txt("Update Patches set usd=0 where Patch_Ser in (" & PatchSer & ")")
    End Sub
    Public Function ShowWithProduct_IdAll(Product_Id As Integer)
        Dim sqlString As String
        sqlString = "SELECT * FROM    Recipe_ProductsTbl "
        sqlString = sqlString & " where Product_Id=" & Product_Id & ""
        Dim DT As New DataTable
        DT.Clear()
        DT = Conn.SELECT_TXT(sqlString)
        Return DT

    End Function

    'Public Function ShowProductIdExpireDate_(Product_Id As Integer, CostCenter_Id As Integer)
    '    Dim DT As New DataTable
    '    DT.Clear()

    '    Dim SqlStr As String

    '    SqlStr = " SELECT       Patch_Ser, Patch_Name, Product_Code, Product_Name, CostCenter_Id, CostCenter_Name, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, "
    '    SqlStr = SqlStr & "  NetQ_Qsetup_CurrentQ, Prud_Date, Exp_Date, CloseOpen, usd, Product_Id "
    '    SqlStr = SqlStr & "   FROM Patches"

    '    SqlStr = SqlStr & " where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & " and CloseOpen=0  order by Exp_Date "
    '    DT = Conn.SELECT_TXT(SqlStr)
    '    Return DT

    'End Function
    Public Function ShowUsdFalse_(Product_Id As Integer, CostCenter_Id As Integer)
        Dim DT As New DataTable
        DT.Clear()

        Dim SqlStr As String

        SqlStr = " SELECT       Patch_Ser, Patch_Name, Product_Code, Product_Name, CostCenter_Id, CostCenter_Name, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, "
        SqlStr = SqlStr & "  NetQ_Qsetup_CurrentQ, Prud_Date, Exp_Date, CloseOpen, usd, Product_Id"
        SqlStr = SqlStr & "   FROM Patches"

        SqlStr = SqlStr & " where Product_Id=" & Product_Id & " and  CostCenter_Id=" & CostCenter_Id & " and CloseOpen=0 and usd=0 order by Exp_Date "
        DT = Conn.SELECT_TXT(SqlStr)
        Return DT
        'Patch_Name='" & Patch_Name & "' and
    End Function
    Public Sub UpdatePatchUse_True(Patch_Ser As Integer)
        Conn.EXECUT_Txt("UPDATE Patches  SET usd=1 where  Patch_Ser=" & Patch_Ser & "")
    End Sub
    Public Function ShowSerOpen_(Patch_Ser As Integer, CostCenter_Id As Integer)
        Dim DT As New DataTable
        DT.Clear()
        'and usd=0
        Dim SqlStr As String

        SqlStr = " SELECT       Patch_Ser, Patch_Name, Product_Code, Product_Name, CostCenter_Id, CostCenter_Name, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, "
        SqlStr = SqlStr & "  NetQ_Qsetup_CurrentQ, Prud_Date, Exp_Date, CloseOpen, usd, Product_Id"
        SqlStr = SqlStr & "   FROM Patches "

        SqlStr = SqlStr & " where Patch_Ser=" & Patch_Ser & "  and CloseOpen=0  and CostCenter_Id=" & CostCenter_Id & ""
        DT = Conn.SELECT_TXT(SqlStr)
        Return DT
        'and usd=0
    End Function
    Public Function CheckSumQPatch(Product_Id As Integer, CostCenter_Id As Integer)
        Dim DT As New DataTable
        DT.Clear()

        Dim SqlStr As String

        SqlStr = " SELECT        CostCenter_Id, Product_Id, SUM(NetQ_Qsetup_CurrentQ) AS PatchQ"
        SqlStr = SqlStr & " FROM  Patches"
        SqlStr = SqlStr & " GROUP BY CostCenter_Id, Product_Id HAVING         Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & ""
        DT = Conn.SELECT_TXT(SqlStr)
        Return DT

    End Function
    Public Function CheckValiDate(CostCenter_Id As Integer, CurrDate As DateTime, CostCenterName As String) As Boolean
        Dim Dt As New DataTable
        Dim Valuereturn As Boolean = False
        Dim MyFromDate, MyToDate As DateTime
        Dim SerProd As Integer


        Dim R As Integer = 2

        For T As Integer = 1 To R
            If T = 1 Then
                Dt.Clear()
                Dt = ShowWithCostCenterIdFilter_(CostCenter_Id, CurrDate)
            Else
                Dt.Clear()
                Dt = ShowWithCostCenterIdFilter_(CostCenter_Id, CurrDate)
            End If
        Next


        If Dt.Rows.Count = 0 Then
            MessageBox.Show("Please Go To Stores For Define Period First For This Cost Center : " & CostCenterName, "Error Store Period", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return Valuereturn
        End If

        SerProd = Val(Dt.Rows(0)("ser"))
        MyFromDate = Convert.ToDateTime(Dt.Rows(0)("From_Period"))
        MyToDate = Convert.ToDateTime(Dt.Rows(0)("To_Priod"))

        Dt.Clear()
        Dt = ShowPeriodCloseWithFilter_(SerProd)

        If Dt.Rows.Count > 0 Then
            MyFromDate = Convert.ToDateTime(Dt.Rows(0)("per_to"))

        End If

        MyFromDate = Format(MyFromDate, "yyyy-MM-dd HH:mm:ss")
        MyToDate = Format(MyToDate, "yyyy-MM-dd HH:mm:ss")
        CurrDate = Format(CurrDate, "yyyy-MM-dd HH:mm:ss")

        If (CurrDate < MyFromDate Or CurrDate > MyToDate) Then
            MsgBox("Sorry Your Date Is Out Of Range Current Period With The Cost Center: " & CostCenterName)
            Return Valuereturn
        End If

        Valuereturn = True
        Return Valuereturn
    End Function





    ' Dictionary to track product quantities already processed in the current transaction
    Private Shared _processedProductQuantities As New Dictionary(Of String, Decimal)
    ' Flag to prevent automatic production creation during ingredient processing
    Private Shared _isProcessingIngredients As Boolean = False

    ''' <summary>
    ''' Resets the processed products tracking for a new transaction
    ''' </summary>
    Public Sub ResetProcessedProducts()
        ' Clear the dictionary of processed products
        If _processedProductQuantities IsNot Nothing Then
            _processedProductQuantities.Clear()
        Else
            _processedProductQuantities = New Dictionary(Of String, Decimal)
        End If
    End Sub

    Public Function GetQuantityNow(Product_Id As Integer, CostCenter_Id As Integer, Product_Code As String, Quntity As Decimal) As Decimal
        Dim Dt As New DataTable
        Dim Q As Decimal = 0
        Dt.Clear()

        ' Check if this product is a recipe
        Dim isRecipe As Boolean = False
        Dim productDt As New DataTable
        productDt = ShowDataParametars_(" Product_Id=" & Product_Id)

        If productDt.Rows.Count > 0 AndAlso Convert.ToBoolean(productDt.Rows(0)("IsRecipe")) Then
            isRecipe = True
        End If

        'sum(Quntity) as
        Dt = Conn.SELECT_TXT("select Quntity from StockOnHandTbl_POS where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & "")
        'Quntity = Quntity * -1

        If Dt.Rows.Count = 0 Then
            ' Always update StockOnHandTbl_POS for all products (recipe and non-recipe)
            ' This ensures proper tracking of all product movements
            Conn.EXECUT_Txt("Insert Into StockOnHandTbl_POS (Product_Id, Product_Code, CostCenter_Id, Quntity) Values (" & Product_Id & ",'" & Product_Code & "', " & CostCenter_Id & ",'" & Quntity & "')")

            ' For recipe products, create automatic production transaction to balance inventory
            ' Only if we're not already processing ingredients (prevents infinite recursion)
            If isRecipe And Not _isProcessingIngredients Then
                CreateAutoProductionTransaction(Product_Id, CostCenter_Id, Product_Code, Math.Abs(Quntity))
            End If
        Else
            Q = CDec(Dt.Rows(0)(0))

            ' Always update StockOnHandTbl_POS for all products (recipe and non-recipe)
            ' This ensures proper tracking of all product movements
            Q = Q + Quntity
            Conn.EXECUT_Txt("update StockOnHandTbl_POS set Quntity='" & Q & "' where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & "")
            Q = Q - Quntity

            ' For recipe products, create automatic production transaction to balance inventory
            ' Only if we're not already processing ingredients (prevents infinite recursion)
            If isRecipe And Not _isProcessingIngredients Then
                CreateAutoProductionTransaction(Product_Id, CostCenter_Id, Product_Code, Math.Abs(Quntity))
            End If
        End If

        Return Q
    End Function

    ''' <summary>
    ''' Recursively processes recipe ingredients, deducting them from inventory.
    ''' For recipe ingredients, always processes their sub-ingredients directly,
    ''' regardless of inventory, to ensure consistent behavior and disable automatic
    ''' production creation.
    ''' </summary>
    ''' <param name="Product_Id">The ID of the product/recipe</param>
    ''' <param name="CostCenter_Id">The cost center ID</param>
    ''' <param name="Quantity">The quantity of the product being used</param>
    Public Sub ProcessRecipeIngredients(Product_Id As Integer, CostCenter_Id As Integer, Quantity As Decimal)
        ' Set flag to prevent automatic production creation during ingredient processing
        Dim wasProcessingIngredients As Boolean = _isProcessingIngredients
        _isProcessingIngredients = True

        Try
            ' Check if this product is a recipe
            Dim productDt As New DataTable
            productDt = ShowDataParametars_(" Product_Id=" & Product_Id)

            If productDt.Rows.Count > 0 AndAlso Convert.ToBoolean(productDt.Rows(0)("IsRecipe")) Then
                ' Get all ingredients for this recipe
                Dim ingredientsDt As New DataTable
                ingredientsDt = ShowWithProduct_IdAll(Product_Id)

                ' Process each ingredient
                For i As Integer = 0 To ingredientsDt.Rows.Count - 1
                    Dim ingredientId As Integer = Convert.ToInt32(ingredientsDt.Rows(i)("Recipe_Product_Id"))
                    Dim ingredientCode As String = ingredientsDt.Rows(i)("Recipe_Product_Code").ToString()
                    Dim usedQuantity As Decimal = Convert.ToDecimal(ingredientsDt.Rows(i)("UsedQuantity"))
                    Dim currentUntQ As Decimal = Convert.ToDecimal(ingredientsDt.Rows(i)("Current_Unt_Q"))
                    Dim untQ As Decimal = Convert.ToDecimal(ingredientsDt.Rows(i)("Unt_Q"))

                    ' Calculate the required quantity of this ingredient
                    ' Use the exact original calculation from the old code
                    Dim requiredQuantity As Decimal = usedQuantity * currentUntQ * Quantity

                    ' Create a key for this ingredient that includes the parent product
                    ' This ensures we track ingredients per parent product, not globally
                    ' IMPORTANT: This key is only for tracking within the same recipe, not across different orders
                    Dim ingredientKey As String = Product_Id.ToString() & "_" & ingredientId.ToString() & "_" & CostCenter_Id.ToString()

                    ' Check if we've already processed this specific ingredient for this specific parent product
                    ' NOTE: This only prevents duplicate processing within the SAME recipe, not across different orders
                    If _processedProductQuantities.ContainsKey(ingredientKey) Then
                        ' Add to the existing quantity instead of skipping
                        _processedProductQuantities(ingredientKey) += requiredQuantity
                        ' Still need to deduct the additional quantity
                        GetQuantityNow(ingredientId, CostCenter_Id, ingredientCode, requiredQuantity)
                        Continue For
                    End If

                    ' Check if this ingredient is itself a recipe
                    Dim ingredientProductDt As New DataTable
                    ingredientProductDt = ShowDataParametars_(" Product_Id=" & ingredientId)

                    ' If the ingredient is a recipe, create production for it and process its ingredients
                    If ingredientProductDt.Rows.Count > 0 AndAlso Convert.ToBoolean(ingredientProductDt.Rows(0)("IsRecipe")) Then
                        ' Add this ingredient to the dictionary of processed products
                        _processedProductQuantities(ingredientKey) = requiredQuantity

                        ' Temporarily restore the flag to allow production creation for this recipe ingredient
                        _isProcessingIngredients = False

                        ' Create automatic production for the recipe ingredient
                        CreateAutoProductionTransaction(ingredientId, CostCenter_Id, ingredientCode, requiredQuantity)

                        ' Set the flag back to prevent production during sub-ingredient processing
                        _isProcessingIngredients = True
                    Else
                        ' Normal case: Deduct this ingredient from inventory
                        GetQuantityNow(ingredientId, CostCenter_Id, ingredientCode, requiredQuantity)

                        ' Add this ingredient to the dictionary of processed products
                        _processedProductQuantities(ingredientKey) = requiredQuantity
                    End If
                Next
            End If
        Finally
            ' Restore the original flag state
            _isProcessingIngredients = wasProcessingIngredients
        End Try
    End Sub
    Public Sub DeleteQuantityNow()
        Conn.EXECUT_Txt("Truncate Table StockOnHandTbl_POS")
    End Sub



    ''' <summary>
    ''' Creates an automatic production transaction for recipe products to balance inventory.
    ''' This adds the recipe product to inventory and deducts its ingredients.
    ''' </summary>
    ''' <param name="Product_Id">The ID of the recipe product</param>
    ''' <param name="CostCenter_Id">The cost center ID</param>
    ''' <param name="Product_Code">The product code</param>
    ''' <param name="Quantity">The quantity to produce</param>
    Private Sub CreateAutoProductionTransaction(Product_Id As Integer, CostCenter_Id As Integer, Product_Code As String, Quantity As Decimal)
        ' Get product details
        Dim productDt As New DataTable
        productDt = ShowDataParametars_(" Product_Id=" & Product_Id)

        If productDt.Rows.Count = 0 Then
            Return
        End If

        Dim productName As String = productDt.Rows(0)("Product_Name").ToString()
        Dim untId As Integer = Convert.ToInt32(productDt.Rows(0)("Unt_Id"))
        Dim untName As String = productDt.Rows(0)("Unt_Name").ToString()
        Dim untGroupId As Integer = Convert.ToInt32(productDt.Rows(0)("Unt_GroupId"))
        Dim untQ As Decimal = Convert.ToDecimal(productDt.Rows(0)("Unt_Q"))
        Dim costProduct As Decimal = Convert.ToDecimal(productDt.Rows(0)("Cost_Product"))

        ' Get a unique transaction code for production
        Dim transactionCode As Integer = GetTransCodeProduction()

        ' Create production transaction header
        Head_Insert(transactionCode, CostCenter_Id, 0, "", "Auto Production for Recipe", transactionCode, 0, 0, 0,
                   False, True, 1, 5, 0, 0, True, DateTime.Now, DateTime.Now, "", 1, "", 7)

        ' Create production transaction detail (adds to inventory)
        Details_Save(transactionCode.ToString(), Product_Id, Product_Code, productName,
                    Quantity, Quantity, Quantity, 0, costProduct, Quantity * costProduct,
                    0, "", CostCenter_Id, "", 0, "", 0, "", False, False,
                    0, "", False, untId, untName, untGroupId, untQ, untId, untName, untQ, Quantity,
                    DateTime.Now, False, True, False, False, "", True, DateTime.Now, True, costProduct)

        ' Process recipe ingredients (this will deduct from ingredients)
        ProcessRecipeIngredients(Product_Id, CostCenter_Id, Quantity)
    End Sub


    Public Function ShowWithCostCenterIdFilterOld_(CostCenter_Id As Integer)
        Dim DT As New DataTable
        Dim Year_Num As Integer
        Year_Num = Val(Date.Now.Year)
        DT.Clear()
        'and Year_Num=" & Year_Num & "
        DT = Conn.SELECT_TXT("SELECT ser, Store_id, Store_Name, Month_No, From_Period, To_Priod, Year_Num, Period_Numm, prod, CostCenter_Id, CostCenter_Name FROM Period_Store where CostCenter_Id=" & CostCenter_Id & " and  (prod = 0)  ORDER BY Year_Num, Period_Numm ")
        Return DT

    End Function
    Public Function ShowWithCostCenterIdFilter_(CostCenter_Id As Integer, Startdate As DateTime)
        Dim DT As New DataTable
        Dim Year_Num, Period_Numm As Integer
        Year_Num = Val(Date.Now.Year)
        ', EndDate As DateTime
        DT.Clear()
        Dim SQL As String = ""
        Dim FoDates, ToDates As String
        FoDates = Format(Startdate, "yyyy-MM/dd 00:00:00")
        ToDates = Format(Startdate, "yyyy-MM/dd 23:59:59")
        Period_Numm = Startdate.Month
        Year_Num = Startdate.Year
        'Dim Year_Num As Integer
        '  SQL = "  and (From_Period >= CONVERT(DATETIME, '" & FoDates & "', 102)) AND (From_Period <= CONVERT(DATETIME,'" & ToDates & "', 102))  "
        'and Year_Num=" & Year_Num & "
        SQL = " And Period_Numm=" & Period_Numm & "  and Year_Num=" & Year_Num & "" 'and  (prod = 0)
        DT = Conn.SELECT_TXT("SELECT ser, Store_id, Store_Name, Month_No, From_Period, To_Priod, Year_Num, Period_Numm, prod, CostCenter_Id, CostCenter_Name FROM Period_Store where CostCenter_Id=" & CostCenter_Id & "  " & SQL & " ORDER BY Year_Num, Period_Numm ")
        Return DT

    End Function
    Public Function ShowPeriodCloseWithFilter_(per_ser As Integer)
        Dim DT As New DataTable
        DT.Clear()
        Dim SqlStr As String

        SqlStr = "SELECT         ser, per_no, per_frm, per_to, Product_Code, Product_Name, itm_opn, itm_cls, clos_dat, clos_tim, per_ser, CostCenter_Id, cls_opn, nam, Product_Id"
        SqlStr = SqlStr & " FROM            Period_Close  where per_ser=" & per_ser & " and cls_opn=1 order by ser desc"
        DT = Conn.SELECT_TXT(SqlStr)
        Return DT

    End Function

    Public Sub Details_Save(Transaction_Code As String, Product_Id As Integer, Product_Code As String, Product_Name As String, Order_Q As Double, Reciving_Q As Double, Invoice_Q As Double, Return_Q As Double, Cost_Product As Double, CostTotalLine As Double, CostCenter_Id_Frm As Integer, CostCenter_Name_Frm As String, CostCenter_Id_To As Integer, CostCenter_Name_To As String, Suppliers_Id_Frm As Integer, Suppliers_Name_Frm As String, Suppliers_Id_To As Integer, Suppliers_Name_To As String, Supplier_Frm As Boolean, Supplier_To As Boolean, Patch_Ser As Integer, Patch_Name As String, IsExpire As Boolean, Unt_Id As Integer, Unt_Name As String, Unt_GroupId As Integer, Unt_Q As Double, Current_Unt_Id As Integer, Current_Unt_Name As String, Current_Unt_Q As Double, NetQ_Qsetup_CurrentQ As Double, Transaction_Date_Create As DateTime, Transaction_Save As Boolean, Transaction_Submit As Boolean, Transaction_Cancel As Boolean, Del As Boolean, Transaction_Patch As String, Authulized As Boolean, Gard As DateTime, IsProductions As Boolean, AvCost As Double, Optional Required_Quantity As Double = 0, Optional UsedQuantity As Double = 0, Optional Rid As Integer = 0)

        Dim Dt As New DataTable
        Dim FieldPro, Prapro As String

        If Rid = 0 Then
            FieldPro = ""
            Prapro = ""
        Else
            FieldPro = ",Product_Id_PRO"
            Prapro = "," & Rid
        End If
        Dim Open_Q, Close_Q, CollectAvCost, Open_QB, Close_QBase, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase, Item_Unit As Double
        Dim soh As Double = 0
        Dim SqlStrDetails, transCreate, GardDate As String
        Dim SqlStrHistory As String
        'transCreate = Format(Transaction_Date_Create, "yyyy-MM-dd")
        If Authulized = True Then
            transCreate = Transaction_Date_Create.ToString("yyyy-MM-dd HH:mm:ss")
            GardDate = Gard.ToString("yyyy-MM-dd HH:mm:ss")

            soh = ClsOnHand.Save_Stock(Product_Id, Product_Name, Product_Code, CostCenter_Id_To, NetQ_Qsetup_CurrentQ, True, soh)

            Dt.Clear()
            Dt = ClsOnHand.Show_StockWithParam(Product_Id, CostCenter_Id_To)

            If Dt.Rows.Count = 0 Then

                Open_QB = 0

            Else

                Open_QB = Convert.ToDouble(Dt.Rows(0)("QuntityBase"))
            End If

            Item_Unit = Unt_Q
            NetQ_Qsetup_CurrentQBase = Reciving_Q * Current_Unt_Q


            Open_Q = NetQ_Qsetup_CurrentQ

            Close_Q = soh


            If IsProductions Then
                Open_Q = Open_Q * -1
                '  Open_QB = Open_QB * -1
            End If
            Open_Q = Close_Q + Open_Q
            Close_QBase = Val(Open_QB) + Val(NetQ_Qsetup_CurrentQBase)
            'CollectAvCost = Val(Close_Q) * Val(Cost_Product)
            'AvCost = Val(AvCost) * Val(Open_Q)
            Cost_ProductPerUnit = Cost_Product

            ClsOnHand.BaseUniteSOH(Product_Id, Product_Code, CostCenter_Id_To, Close_QBase, Unt_Q)

            'AvCost += CollectAvCost
            'AvCost = Val(AvCost) / Val(Close_Q)
            '//////////////////////////////// TransFer (IN) ///////////////////////////////////////////////////////////////////
            SqlStrDetails = " INSERT INTO Transaction_DetailsTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Inventory,Open_Q,Close_Q,AvCost, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase " & FieldPro & ") VALUES"
            SqlStrDetails = SqlStrDetails & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & AvCost & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",7,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Authulized & "'," & soh & ",'" & GardDate & "',0,'" & Open_Q & "','" & Close_Q & "','" & AvCost & "','" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "'" & Prapro & ")"
            Conn.EXECUT_Txt(SqlStrDetails)

            SqlStrHistory = " INSERT INTO Transaction_Details_HistoryTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Inventory,Open_Q,Close_Q,AvCost, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase" & FieldPro & ") VALUES"
            SqlStrHistory = SqlStrHistory & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & AvCost & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",7,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Authulized & "'," & soh & ",'" & GardDate & "',0,'" & Open_Q & "','" & Close_Q & "','" & AvCost & "','" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "'" & Prapro & ")"
            Conn.EXECUT_Txt(SqlStrHistory)

            If IsProductions Then
                ClsOnHand.UpdateAvPrice(Product_Id, AvCost, AvCost, CostCenter_Id_To, Product_Code)
            End If
            'Patch_Ser,Patch_Name,

            If IsExpire = True Then
                ClsOnHand.Update_PatchTransFer(Patch_Ser, Product_Id, Product_Code, Product_Name, CostCenter_Id_To, CostCenter_Name_To, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, NetQ_Qsetup_CurrentQ, True)
            End If





        Else
            '//////////////////////////////// TransFer (OUT) ///////////////////////////////////////////////////////////////////
            transCreate = Transaction_Date_Create.ToString("yyyy-MM-dd HH:mm:ss")
            GardDate = Gard.ToString("yyyy-MM-dd HH:mm:ss")

            Dim CostFromId, CostToId As Integer
            Dim CostFromName, CostToName As String
            '  CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To
            CostFromId = CostCenter_Id_Frm
            CostFromName = CostCenter_Name_Frm
            CostToId = CostCenter_Id_To
            CostToName = CostCenter_Name_To


            CostCenter_Id_To = CostFromId
            CostCenter_Name_To = CostFromName
            CostCenter_Name_Frm = CostToName
            CostCenter_Id_Frm = CostToId

            soh = ClsOnHand.Save_Stock(Product_Id, Product_Name, Product_Code, CostCenter_Id_To, NetQ_Qsetup_CurrentQ, False, soh)

            Reciving_Q = Val(Reciving_Q) * -1
            NetQ_Qsetup_CurrentQ = Val(NetQ_Qsetup_CurrentQ) * -1
            Open_QB = 0
            Close_QBase = 0
            '///////////////////////////////////////////////////////////////////////////////
            Dt.Clear()
            Dt = ClsOnHand.Show_StockWithParam(Product_Id, CostCenter_Id_To)

            If Dt.Rows.Count = 0 Then

                Open_QB = 0

            Else

                Open_QB = Convert.ToDouble(Dt.Rows(0)("QuntityBase"))
            End If
            NetQ_Qsetup_CurrentQBase = Required_Quantity * UsedQuantity * Current_Unt_Q
            Item_Unit = Unt_Q
            ' NetQ_Qsetup_CurrentQBase = (NetQ_Qsetup_CurrentQBase * Unt_Q) 'Current_Unt_Q) / Unt_Q

            '///////////////////////////////////////////////////////////////////////////////

            Open_Q = NetQ_Qsetup_CurrentQ
            Close_Q = soh
            If IsProductions = False Then
                Open_Q = Open_Q * -1
                ' Open_QB = Open_QB * -1
            End If
            Close_QBase = Val(Open_QB) - Val(NetQ_Qsetup_CurrentQBase)
            '   Unt_Q = Unt_Q
            Open_Q = Close_Q + Open_Q

            AvCost = Cost_Product
            Cost_ProductPerUnit = Cost_Product
            ClsOnHand.BaseUniteSOH(Product_Id, Product_Code, CostCenter_Id_To, Close_QBase, Unt_Q)

            NetQ_Qsetup_CurrentQBase = Math.Abs(NetQ_Qsetup_CurrentQBase)
            NetQ_Qsetup_CurrentQBase = NetQ_Qsetup_CurrentQBase * -1
            Dim Okkay As Boolean
            Okkay = True
            SqlStrDetails = " INSERT INTO Transaction_DetailsTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Inventory,Open_Q,Close_Q,AvCost, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase" & FieldPro & ") VALUES"
            SqlStrDetails = SqlStrDetails & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & Cost_Product & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",7,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Okkay & "'," & soh & ",'" & GardDate & "',0,'" & Open_Q & "','" & Close_Q & "','" & AvCost & "','" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "'" & Prapro & ")"
            Conn.EXECUT_Txt(SqlStrDetails)

            SqlStrHistory = " INSERT INTO Transaction_Details_HistoryTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Inventory,Open_Q,Close_Q,AvCost, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase" & FieldPro & ") VALUES"
            SqlStrHistory = SqlStrHistory & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & Cost_Product & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",7,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Okkay & "'," & soh & ",'" & GardDate & "',0,'" & Open_Q & "','" & Close_Q & "','" & AvCost & "','" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "'" & Prapro & ")"
            Conn.EXECUT_Txt(SqlStrHistory)


            'Patch_Ser,Patch_Name,
            Reciving_Q = Val(Reciving_Q) * -1
            NetQ_Qsetup_CurrentQ = Val(NetQ_Qsetup_CurrentQ) * -1
            If IsExpire = True Then
                ClsOnHand.Update_PatchTransFer(Patch_Ser, Product_Id, Product_Code, Product_Name, CostCenter_Id_To, CostCenter_Name_To, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, NetQ_Qsetup_CurrentQ, False)
            End If



        End If

    End Sub


    Public Function Details_SaveSales(Transaction_Code As String, Product_Id As Integer, Product_Code As String, Product_Name As String, Order_Q As Double, Reciving_Q As Double, Invoice_Q As Double, Return_Q As Double, Cost_Product As Double, CostTotalLine As Double, CostCenter_Id_Frm As Integer, CostCenter_Name_Frm As String, CostCenter_Id_To As Integer, CostCenter_Name_To As String, Suppliers_Id_Frm As Integer, Suppliers_Name_Frm As String, Suppliers_Id_To As Integer, Suppliers_Name_To As String, Supplier_Frm As Boolean, Supplier_To As Boolean, Patch_Ser As Integer, Patch_Name As String, IsExpire As Boolean, Unt_Id As Integer, Unt_Name As String, Unt_GroupId As Integer, Unt_Q As Double, Current_Unt_Id As Integer, Current_Unt_Name As String, Current_Unt_Q As Double, NetQ_Qsetup_CurrentQ As Double, Transaction_Date_Create As DateTime, Transaction_Save As Boolean, Transaction_Submit As Boolean, Transaction_Cancel As Boolean, Del As Boolean, Transaction_Patch As String, Authulized As Boolean, Gard As DateTime) As Integer
        Dim Open_Q, Close_Q, AvCost, Open_QB, Close_QBase, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase, Item_Unit As Decimal
        Dim Dt As New DataTable
        Dim soh As Decimal = 0
        '  If Transaction_Submit = True And Authulized = True Then
        Dt.Clear()
        '  Dt = ClsProduct.ShowProduct_(Product_Id)
        Dt = ClsOnHand.Show_StockWithParam(Product_Id, CostCenter_Id_To)
        Dim OldAvPrice As Decimal
        If Dt.Rows.Count = 0 Then
            OldAvPrice = 0
        Else

            OldAvPrice = Dt.Rows(0)("AvCost")
        End If
        Cost_Product = OldAvPrice
        AvCost = Cost_Product
        CostTotalLine = NetQ_Qsetup_CurrentQ * AvCost
        '//////////////////// Get Open And Close'//////////////////////////////////////////
        Dt.Clear()
        Dt = ClsOnHand.Show_StockWithParam(Product_Id, CostCenter_Id_To)

        If Dt.Rows.Count = 0 Then

            Open_QB = 0
            Open_Q = 0
        Else
            Open_Q = Convert.ToDouble(Dt.Rows(0)("Quntity"))
            Open_QB = Convert.ToDouble(Dt.Rows(0)("QuntityBase"))
        End If

        Cost_ProductPerUnit = Cost_Product
        Item_Unit = Unt_Q
        NetQ_Qsetup_CurrentQBase = Reciving_Q * Current_Unt_Q



        Close_QBase = Val(Open_QB) + Val(NetQ_Qsetup_CurrentQBase)
        Close_Q = Val(Open_Q) + Val(NetQ_Qsetup_CurrentQ)

        '////////////////////////////////////////Av Prise//////////////////////////////////
        ''Dt.Clear()
        ''   Dt = ClsProduct.ShowProduct_(Product_Id)
        ''Dt = ClsOnHand.Show_StockWithParam(Product_Id, CostCenter_Id_To)
        ''Dim OldAvPrice, NewAvPrice As Decimal
        ''If Dt.Rows.Count = 0 Then
        ''    OldAvPrice = 0
        ''Else

        ''    OldAvPrice = Dt.Rows(0)("AvCost")
        ''End If

        ''OldAvPrice = Val(OldAvPrice) * Val(Open_Q)

        ''NewAvPrice = Val(Cost_Product) * Val(NetQ_Qsetup_CurrentQ)

        ''OldAvPrice += Val(NewAvPrice)

        ''If Close_Q <> 0 Then
        ''    AvCost = OldAvPrice / Close_Q
        ''Else
        ''    Close_Q = 0
        ''    AvCost = 0
        ''End If
        ''If AvCost > 0 Then

        ''Else
        ''    AvCost = 0
        ''End If
        'MessageBox.Show(AvCost * Close_Q)
        ' AvCost = Cost_Product
        soh = ClsOnHand.Save_Stock(Product_Id, Product_Name, Product_Code, CostCenter_Id_To, NetQ_Qsetup_CurrentQ, False, soh, Cost_Product, AvCost)

        ClsOnHand.BaseUniteSOH(Product_Id, Product_Code, CostCenter_Id_To, Close_QBase, Unt_Q)
        ' End If


        Dim SqlStrDetails, transCreate, GardDate As String
        Dim SqlStrHistory As String
        'transCreate = Format(Transaction_Date_Create, "yyyy-MM-dd")

        transCreate = Transaction_Date_Create.ToString("yyyy-MM-dd HH:mm:ss")
        GardDate = Gard.ToString("yyyy-MM-dd HH:mm:ss")
        '//////////////////////////////// TransFer (IN) ///////////////////////////////////////////////////////////////////
        SqlStrDetails = " INSERT INTO Transaction_DetailsTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Open_Q,Close_Q,AvCost,Inventory, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase) VALUES"
        SqlStrDetails = SqlStrDetails & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & Cost_Product & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",9,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Authulized & "'," & soh & ",'" & GardDate & "','" & Open_Q & "','" & Close_Q & "','" & AvCost & "',0,'" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "')"
        Conn.EXECUT_Txt(SqlStrDetails)


        SqlStrHistory = " INSERT INTO Transaction_Details_HistoryTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Open_Q,Close_Q,AvCost,Inventory, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase) VALUES"
        SqlStrHistory = SqlStrHistory & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & Cost_Product & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",9,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Authulized & "'," & soh & ",'" & GardDate & "','" & Open_Q & "','" & Close_Q & "','" & AvCost & "',0, '" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "')"
        Conn.EXECUT_Txt(SqlStrHistory)

        'Patch_Ser,Patch_Name,

        If IsExpire = True And Transaction_Submit = True And Authulized = True Then
            ClsOnHand.Update_PatchTransFer(Patch_Ser, Product_Id, Product_Code, Product_Name, CostCenter_Id_To, CostCenter_Name_To, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, NetQ_Qsetup_CurrentQ, False)
        End If

        Dim transSer As Integer = 0
        Dim DtSer As New DataTable
        DtSer.Clear()
        DtSer = Conn.SELECT_TXT("select * from Transaction_DetailsTbl where Transaction_Code=" & Transaction_Code & " and Transaction_Id=9 ")
        'Ser
        If DtSer.Rows.Count > 0 Then
            transSer = Convert.ToInt32(DtSer.Rows(0)("Ser"))
        End If

        '//////////////////////////////// TransFer (OUT) ///////////////////////////////////////////////////////////////////
        'Dim CostFromId, CostToId As Integer
        'Dim CostFromName, CostToName As String
        ''  CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To
        'CostFromId = CostCenter_Id_Frm
        'CostFromName = CostCenter_Name_Frm
        'CostToId = CostCenter_Id_To
        'CostToName = CostCenter_Name_To


        'CostCenter_Id_To = CostFromId
        'CostCenter_Name_To = CostFromName
        'CostCenter_Name_Frm = CostToName
        'CostCenter_Id_Frm = CostToId

        soh = 0
        'If Transaction_Submit = True And Authulized = True Then
        '    Open_Q = 0
        '    Close_Q = 0
        '    AvCost = 0

        '    Open_QB = 0
        '    Close_QBase = 0
        '    '//////////////////// Get Open And Close'//////////////////////////////////////////
        '    Dt.Clear()
        '    Dt = ClsOnHand.Show_StockWithParam(Product_Id, CostCenter_Id_To)

        '    If Dt.Rows.Count = 0 Then
        '        Open_Q = 0
        '    Else
        '        Open_Q = Convert.ToDouble(Dt.Rows(0)("Quntity"))
        '        Open_QB = Convert.ToDouble(Dt.Rows(0)("QuntityBase"))
        '    End If
        '    NetQ_Qsetup_CurrentQBase = Reciving_Q * Current_Unt_Q
        '    Close_QBase = Val(Open_QB) - Val(NetQ_Qsetup_CurrentQBase)


        '    Close_Q = Val(Open_Q) - Val(NetQ_Qsetup_CurrentQ)
        '    '////////////////////////////////////////Av Prise//////////////////////////////////
        '    'Dt.Clear()
        '    'Dt = ClsProduct.ShowProduct_(Product_Id)

        '    'Dim OldAvPrice, NewAvPrice As Double
        '    'If Dt.Rows.Count = 0 Then
        '    '    OldAvPrice = 0
        '    'Else

        '    '    OldAvPrice = (Convert.ToDouble(Dt.Rows(0)("AvCost")))
        '    'End If

        '    'OldAvPrice = Math.Round(Val(OldAvPrice) * Val(Open_Q), 2)

        '    'NewAvPrice = Math.Round(Val(Cost_Product) * Val(NetQ_Qsetup_CurrentQ), 2)

        '    'OldAvPrice += Val(NewAvPrice)

        '    'AvCost = Math.Round(Val(OldAvPrice) / Val(Close_Q), 2)

        '    'ClsProduct.updateAvpriceAndCost(Product_Id, Cost_Product, AvCost)

        '    AvCost = Cost_Product

        'soh = ClsOnHand.Save_Stock(Product_Id, Product_Name, Product_Code, CostCenter_Id_To, NetQ_Qsetup_CurrentQ, False, soh)
        'ClsOnHand.BaseUniteSOH(Product_Id, Product_Code, CostCenter_Id_To, Close_QBase, Unt_Q)
        'End If

        'Reciving_Q = Val(Reciving_Q) * -1
        'NetQ_Qsetup_CurrentQBase = Val(NetQ_Qsetup_CurrentQBase) * -1

        'NetQ_Qsetup_CurrentQ = Val(NetQ_Qsetup_CurrentQ) * -1
        'CostTotalLine = CostTotalLine * -1

        'SqlStrDetails = " INSERT INTO Transaction_DetailsTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Open_Q,Close_Q,AvCost,Inventory, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase) VALUES"
        'SqlStrDetails = SqlStrDetails & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & Cost_Product & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",5,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Authulized & "'," & soh & ",'" & GardDate & "','" & Open_Q & "','" & Close_Q & "','" & AvCost & "',0, '" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "')"
        'Conn.EXECUT_Txt(SqlStrDetails)

        'SqlStrHistory = " INSERT INTO Transaction_Details_HistoryTbl (Transaction_Code,Product_Id,Product_Code,Product_Name,Order_Q,Reciving_Q,Invoice_Q,Return_Q,Cost_Product,CostTotalLine,CostCenter_Id_Frm,CostCenter_Name_Frm,CostCenter_Id_To,CostCenter_Name_To,Suppliers_Id_Frm,Suppliers_Name_Frm,Suppliers_Id_To,Suppliers_Name_To,Supplier_Frm,Supplier_To,Patch_Ser,Patch_Name,IsExpire,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Save,Transaction_Submit,Transaction_Cancel,Del,Transaction_Patch,Authulized,SOH,GardDate,Open_Q,Close_Q,AvCost,Inventory, Cost_ProductPerUnit, NetQ_Qsetup_CurrentQBase) VALUES"
        'SqlStrHistory = SqlStrHistory & "('" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "'," & Order_Q & "," & Reciving_Q & "," & Invoice_Q & "," & Return_Q & "," & Cost_Product & "," & CostTotalLine & "," & CostCenter_Id_Frm & ",'" & CostCenter_Name_Frm & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "'," & Suppliers_Id_Frm & " ,'" & Suppliers_Name_Frm & "'," & Suppliers_Id_To & ",'" & Suppliers_Name_To & "','" & Supplier_Frm & "','" & Supplier_To & "'," & Patch_Ser & ",'" & Patch_Name & "','" & IsExpire & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & "," & Unt_Q & "," & Current_Unt_Id & ",'" & Current_Unt_Name & "'," & Current_Unt_Q & "," & NetQ_Qsetup_CurrentQ & ",5,'" & transCreate & "','" & Transaction_Save & "','" & Transaction_Submit & "','" & Transaction_Cancel & "','" & Del & "','" & Transaction_Patch & "','" & Authulized & "'," & soh & ",'" & GardDate & "','" & Open_Q & "','" & Close_Q & "','" & AvCost & "',0, '" & Cost_ProductPerUnit & "','" & NetQ_Qsetup_CurrentQBase & "')"
        'Conn.EXECUT_Txt(SqlStrHistory)

        ''Patch_Ser,Patch_Name,
        'Reciving_Q = Val(Reciving_Q) * -1
        'NetQ_Qsetup_CurrentQ = Val(NetQ_Qsetup_CurrentQ) * -1
        'If IsExpire = True And Transaction_Submit = True And Authulized = True Then
        '    ClsOnHand.Update_PatchTransFer(Patch_Ser, Product_Id, Product_Code, Product_Name, CostCenter_Id_To, CostCenter_Name_To, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, NetQ_Qsetup_CurrentQ, False)
        'End If

        'If Transaction_Submit = True And Authulized = True Then
        '    ClsOnHand.Save_Stock(Product_Id, Product_Name, Product_Code, CostCenter_Id_To, NetQ_Qsetup_CurrentQ, False)
        'End If

        'If Transaction_Submit = True And Authulized = True Then
        CostcenterLinkItems(CostCenter_Id_To, CostCenter_Name_To, Product_Id, Product_Name, Product_Code)
        ' End If
        Return transSer
    End Function

    Public Sub CostcenterLinkItems(CostCenter_Id As Integer, CostCenter_Name As String, Product_Id As Integer, Product_Name As String, Product_Code As String)
        Dim Dt As New DataTable
        Dt.Clear()
        Dim SqlStr, SqlStrHistory As String

        SqlStr = "select * from ItmCostCenterLink where CostCenter_Id=" & CostCenter_Id & " and Product_Id=" & Product_Id & ""


        Dt = Conn.SELECT_TXT(SqlStr)

        If Dt.Rows.Count = 0 Then
            SqlStrHistory = "Insert Into ItmCostCenterLink (CostCenter_Id, CostCenter_Name, Product_Id, Product_Name, Product_Code) Values (" & CostCenter_Id & " ,'" & CostCenter_Name & "'," & Product_Id & ",'" & Product_Name & "','" & Product_Code & "')"
            ''Else

            'SqlStrHistory = "Update ItmCostCenterLink set Cost_Product='" & Cost_Product & "',AvCost='" & AvCost & "' where Suppliers_Id=" & Suppliers_Id & " and Product_Id=" & Product_Id & ""
            Conn.EXECUT_Txt(SqlStrHistory)
        End If


    End Sub


    Public Sub SaveSalesPos(CostCenterPOS_Id As Integer,
                            CostCenterPOS_Name As String,
                            CostCenter_Id_To As Integer,
                            CostCenter_Name_To As String,
                            Transaction_Code As Integer,
                            Product_Id As Integer,
                            Product_Code As String,
                            Product_Name As String,
                            Reciving_Q As Double,
                            Cost_Product As Double,
                            CostTotalLine As Double,
                            Patch_Ser As Integer,
                            Patch_Name As String,
                            Unt_Id As Integer,
                            Unt_Name As String,
                            Unt_GroupId As Integer,
                            Unt_Q As Double,
                            Current_Unt_Id As Integer,
                            Current_Unt_Name As String,
                            Current_Unt_Q As Double,
                            NetQ_Qsetup_CurrentQ As Double,
                            Transaction_Id As Integer,
                            Transaction_Date_CreateA As DateTime,
                            Transaction_Submit As Boolean,
                            TransactionDetails_Ser As Integer,
                            Authulized As Boolean,
                            SOH As Double,
                            Open_Q As Double,
                            Close_Q As Double,
                            Transaction_Patch As String,
                            Check_No As Integer,
                            TotalAvg As Double,
                            Is_Recipy As Boolean,
                            Is_Production As Boolean,
                            CompanyPOS_Id As Integer,
                            CompanyPOS_Name As String,
                            OutLetPOS_Id As Integer,
                            OutLetPOS_Name As String,
                            MethodOfPayment_Id As Integer,
                            MethodOfPayment_Name As String,
                            Sales_Price As Double,
                            IsExpire As Boolean,
                            ProductionCode As String)

        Cost_Product = Getproductcost(Product_Id, CostCenter_Id_To)

        TotalAvg = NetQ_Qsetup_CurrentQ * Cost_Product
        Dim Sql, Transaction_Date_Create As String
        Transaction_Date_Create = Transaction_Date_CreateA.ToString("yyyy-MM-dd HH:mm:ss")

        Sql = "   INSERT INTO Sales_POS (CostCenterPOS_Id,CostCenterPOS_Name ,CostCenter_Id_To,CostCenter_Name_To,Transaction_Code,Product_Id,Product_Code,Product_Name,Reciving_Q,Cost_Product,CostTotalLine,Patch_Ser,Patch_Name,Unt_Id,Unt_Name,Unt_GroupId,Unt_Q,Current_Unt_Id,Current_Unt_Name,Current_Unt_Q,NetQ_Qsetup_CurrentQ,Transaction_Id,Transaction_Date_Create,Transaction_Submit,TransactionDetails_Ser,Authulized,SOH,Open_Q,Close_Q,Transaction_Patch,Check_No,TotalAvg,Is_Recipy,Is_Production,CompanyPOS_Id,CompanyPOS_Name,OutLetPOS_Id,OutLetPOS_Name,MethodOfPayment_Id,MethodOfPayment_Name,Sales_Price,IsExpire,ProductionCode)"
        Sql = Sql & "    VALUES(" & CostCenterPOS_Id & ",'" & CostCenterPOS_Name & "'," & CostCenter_Id_To & ",'" & CostCenter_Name_To & "','" & Transaction_Code & "'," & Product_Id & ",'" & Product_Code & "','" & Product_Name & "','" & Reciving_Q & "','" & Cost_Product & "','" & CostTotalLine & "'," & Patch_Ser & ",'" & Patch_Name & "'," & Unt_Id & ",'" & Unt_Name & "'," & Unt_GroupId & ",'" & Unt_Q & "'," & Current_Unt_Id & " "
        Sql = Sql & " ,'" & Current_Unt_Name & "','" & Current_Unt_Q & "','" & NetQ_Qsetup_CurrentQ & "'," & Transaction_Id & ",'" & Transaction_Date_Create & "','" & Transaction_Submit & "'," & TransactionDetails_Ser & ",'" & Authulized & "','" & SOH & "','" & Open_Q & "','" & Close_Q & "','" & Transaction_Patch & "'," & Check_No & ",'" & TotalAvg & "','" & Is_Recipy & "','" & Is_Production & "'," & CompanyPOS_Id & ",'" & CompanyPOS_Name & "'," & OutLetPOS_Id & ",'" & OutLetPOS_Name & "'," & MethodOfPayment_Id & ",'" & MethodOfPayment_Name & "','" & Sales_Price & "','" & IsExpire & "','" & ProductionCode & "') "

        Conn.EXECUT_Txt(Sql)
    End Sub
    Private Function Getproductcost(Product_Id As Integer, CostCenter_Id_To As Integer) As Double
        Dim Qu As Double = 0
        Dim Dt As New DataTable
        Dt = Conn.SELECT_TXT("select * from   StockOnHandTbl WHERE  (Product_Id =" & Product_Id & ") AND (CostCenter_Id =" & CostCenter_Id_To & ")")

        If Dt.Rows.Count > 0 Then
            Qu = Convert.ToDouble(Dt.Rows(0)("AvCost"))
        End If
        Return Qu
    End Function
    Public Function ShowProductIdExpireDate_(Product_Id As Integer, CostCenter_Id As Integer)
        Dim DT As New DataTable
        DT.Clear()

        Dim SqlStr As String

        SqlStr = " SELECT       Patch_Ser, Patch_Name, Product_Code, Product_Name, CostCenter_Id, CostCenter_Name, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Current_Unt_Id, Current_Unt_Name, Current_Unt_Q, "
        SqlStr = SqlStr & "  NetQ_Qsetup_CurrentQ, Prud_Date, Exp_Date, CloseOpen, usd, Product_Id "
        SqlStr = SqlStr & "   FROM Patches"

        SqlStr = SqlStr & " where Product_Id=" & Product_Id & " and CostCenter_Id=" & CostCenter_Id & " and CloseOpen=0  order by Exp_Date "
        DT = Conn.SELECT_TXT(SqlStr)
        Return DT

    End Function

    Public Function ShowDataParametars_(Conditions As String)
        Dim sqlString As String
        sqlString = "SELECT   Product_Id, Product_Name, Product_Code, Product_BrandId, Product_BrandName, Unt_Id, Unt_Name, Unt_GroupId, Unt_Q, Depart_Id, Group_Id, SubGroup_Id, Cost_Product, AvCost, Consumption, "
        sqlString = sqlString & " SalesPrice, MinStock, MaxStock, ReOrder, Notes, IsStock, IsRecipe, IsExpire, IsProduction, IsSales, Auth "
        sqlString = sqlString & " FROM   ProductsTbl where "

        Dim DT As New DataTable
        DT.Clear()
        DT = Conn.SELECT_TXT(sqlString & Conditions & " order by Product_Code ")
        Return DT

    End Function
End Class
