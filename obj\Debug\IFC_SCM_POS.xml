﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
IFC_SCM_POS
</name>
</assembly>
<members>
<member name="T:IFC_SCM_POS.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:IFC_SCM_POS.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:IFC_SCM_POS.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="M:IFC_SCM_POS.IFC_Transaction.ResetProcessedProducts">
 <summary>
 Resets the processed products tracking for a new transaction
 </summary>
</member>
<member name="M:IFC_SCM_POS.IFC_Transaction.ProcessRecipeIngredients(System.Int32,System.Int32,System.Decimal)">
 <summary>
 Recursively processes recipe ingredients, deducting them from inventory.
 For recipe ingredients, always processes their sub-ingredients directly,
 regardless of inventory, to ensure consistent behavior and disable automatic
 production creation.
 </summary>
 <param name="Product_Id">The ID of the product/recipe</param>
 <param name="CostCenter_Id">The cost center ID</param>
 <param name="Quantity">The quantity of the product being used</param>
</member>
<member name="M:IFC_SCM_POS.IFC_Transaction.CreateAutoProductionTransaction(System.Int32,System.Int32,System.String,System.Decimal)">
 <summary>
 Creates an automatic production transaction for recipe products to balance inventory.
 This adds the recipe product to inventory and deducts its ingredients.
 </summary>
 <param name="Product_Id">The ID of the recipe product</param>
 <param name="CostCenter_Id">The cost center ID</param>
 <param name="Product_Code">The product code</param>
 <param name="Quantity">The quantity to produce</param>
</member>
</members>
</doc>
